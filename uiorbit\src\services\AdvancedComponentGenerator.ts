import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { AIService } from './AIService';
import { DesignSystemAnalyzer, DesignTokens } from './DesignSystemAnalyzer';
import { ModernUIKnowledgeBase } from './ModernUIKnowledgeBase';
import { ContextEngineService } from './ContextEngineService';

export interface ComponentSpec {
  name: string;
  type: 'functional' | 'class' | 'hook' | 'utility';
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion' | 'css-modules';
  props?: PropDefinition[];
  features?: ComponentFeature[];
  accessibility?: boolean;
  responsive?: boolean;
  animations?: boolean;
  testing?: boolean;
  storybook?: boolean;
  documentation?: boolean;
}

export interface PropDefinition {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description: string;
}

export interface ComponentFeature {
  name: string;
  description: string;
  implementation: string;
}

export interface GeneratedComponent {
  name: string;
  framework: string;
  files: {
    component: GeneratedFile;
    styles: GeneratedFile;
    types?: GeneratedFile;
    tests?: GeneratedFile;
    stories?: GeneratedFile;
    documentation?: GeneratedFile;
  };
  dependencies: string[];
  devDependencies: string[];
  usage: string;
  preview?: string;
  metadata: ComponentMetadata;
}

export interface GeneratedFile {
  filename: string;
  content: string;
  language: string;
}

export interface ComponentMetadata {
  category: string;
  complexity: 'simple' | 'medium' | 'complex';
  performance: 'high' | 'medium' | 'low';
  accessibility: boolean;
  responsive: boolean;
  browserSupport: string[];
  estimatedSize: string;
  renderTime: string;
}

export interface GenerationOptions {
  includeTests: boolean;
  includeStories: boolean;
  includeDocumentation: boolean;
  includeTypes: boolean;
  optimizePerformance: boolean;
  ensureAccessibility: boolean;
  makeResponsive: boolean;
  addAnimations: boolean;
  followDesignSystem: boolean;
  modernPatterns: boolean;
}

export class AdvancedComponentGenerator {
  private aiService: AIService;
  private designSystemAnalyzer: DesignSystemAnalyzer;
  private knowledgeBase: ModernUIKnowledgeBase;
  private contextEngine: ContextEngineService;

  constructor(private serviceRegistry: ServiceRegistry) {
    this.aiService = serviceRegistry.get<AIService>('aiService');
    this.designSystemAnalyzer = serviceRegistry.get<DesignSystemAnalyzer>('designSystemAnalyzer');
    this.knowledgeBase = serviceRegistry.get<ModernUIKnowledgeBase>('modernUIKnowledgeBase');
    this.contextEngine = serviceRegistry.get<ContextEngineService>('contextEngineService');
  }

  async generateComponent(spec: ComponentSpec, options: GenerationOptions): Promise<GeneratedComponent> {
    Logger.info(`Generating ${spec.framework} component: ${spec.name}`);

    try {
      // 1. Gather context and design system information
      const context = await this.gatherGenerationContext(spec, options);

      // 2. Generate component code
      const componentCode = await this.generateComponentCode(spec, context, options);

      // 3. Generate styles
      const stylesCode = await this.generateStyles(spec, context, options);

      // 4. Generate TypeScript types (if needed)
      const typesCode = options.includeTypes ? await this.generateTypes(spec, context) : null;

      // 5. Generate tests (if requested)
      const testsCode = options.includeTests ? await this.generateTests(spec, context) : null;

      // 6. Generate Storybook stories (if requested)
      const storiesCode = options.includeStories ? await this.generateStories(spec, context) : null;

      // 7. Generate documentation (if requested)
      const docsCode = options.includeDocumentation ? await this.generateDocumentation(spec, context) : null;

      // 8. Analyze dependencies
      const dependencies = await this.analyzeDependencies(spec, options);

      // 9. Generate usage examples
      const usage = await this.generateUsageExamples(spec, context);

      // 10. Create component metadata
      const metadata = await this.generateMetadata(spec, context, options);

      const generatedComponent: GeneratedComponent = {
        name: spec.name,
        framework: spec.framework,
        files: {
          component: {
            filename: this.getComponentFilename(spec),
            content: componentCode,
            language: this.getLanguageForFramework(spec.framework)
          },
          styles: {
            filename: this.getStylesFilename(spec),
            content: stylesCode,
            language: this.getStylesLanguage(spec.styling)
          },
          ...(typesCode && {
            types: {
              filename: `${spec.name}.types.ts`,
              content: typesCode,
              language: 'typescript'
            }
          }),
          ...(testsCode && {
            tests: {
              filename: this.getTestFilename(spec),
              content: testsCode,
              language: this.getLanguageForFramework(spec.framework)
            }
          }),
          ...(storiesCode && {
            stories: {
              filename: `${spec.name}.stories.${this.getFileExtension(spec.framework)}`,
              content: storiesCode,
              language: this.getLanguageForFramework(spec.framework)
            }
          }),
          ...(docsCode && {
            documentation: {
              filename: `${spec.name}.md`,
              content: docsCode,
              language: 'markdown'
            }
          })
        },
        dependencies: dependencies.runtime,
        devDependencies: dependencies.dev,
        usage,
        metadata
      };

      Logger.info(`Component ${spec.name} generated successfully`);
      return generatedComponent;

    } catch (error) {
      Logger.error(`Failed to generate component ${spec.name}:`, error);
      throw new Error(`Component generation failed: ${error.message}`);
    }
  }

  async generateFromPrompt(prompt: string, options: GenerationOptions): Promise<GeneratedComponent> {
    Logger.info('Generating component from natural language prompt');

    try {
      // 1. Parse the prompt to extract component specification
      const spec = await this.parsePromptToSpec(prompt);

      // 2. Generate the component
      return await this.generateComponent(spec, options);

    } catch (error) {
      Logger.error('Failed to generate component from prompt:', error);
      throw new Error(`Prompt-based generation failed: ${error.message}`);
    }
  }

  async generateVariants(baseComponent: GeneratedComponent, variantTypes: string[]): Promise<GeneratedComponent[]> {
    Logger.info(`Generating variants for component: ${baseComponent.name}`);

    const variants: GeneratedComponent[] = [];

    for (const variantType of variantTypes) {
      try {
        const variantSpec = await this.createVariantSpec(baseComponent, variantType);
        const variant = await this.generateComponent(variantSpec, {
          includeTests: true,
          includeStories: true,
          includeDocumentation: true,
          includeTypes: true,
          optimizePerformance: true,
          ensureAccessibility: true,
          makeResponsive: true,
          addAnimations: false,
          followDesignSystem: true,
          modernPatterns: true
        });

        variants.push(variant);

      } catch (error) {
        Logger.warn(`Failed to generate variant ${variantType}:`, error);
      }
    }

    return variants;
  }

  private async gatherGenerationContext(spec: ComponentSpec, options: GenerationOptions): Promise<any> {
    const context: any = {
      projectContext: await this.contextEngine.getCurrentContext(),
      designTokens: null,
      modernPatterns: [],
      frameworkFeatures: null
    };

    // Get design system information if available
    if (options.followDesignSystem) {
      try {
        const designSystemAnalysis = await this.designSystemAnalyzer.analyzeWorkspaceDesignSystem();
        context.designTokens = designSystemAnalysis.tokens;
      } catch (error) {
        Logger.warn('Could not analyze design system:', error);
      }
    }

    // Get modern patterns and recommendations
    if (options.modernPatterns) {
      context.modernPatterns = await this.knowledgeBase.getRecommendations({
        framework: spec.framework,
        responsive: options.makeResponsive,
        accessibility: options.ensureAccessibility
      });
    }

    // Get framework-specific features
    context.frameworkFeatures = await this.knowledgeBase.getFrameworkFeatures(spec.framework);

    return context;
  }

  private async generateComponentCode(spec: ComponentSpec, context: any, options: GenerationOptions): Promise<string> {
    const prompt = this.buildComponentPrompt(spec, context, options);

    const response = await this.aiService.generateCode(prompt, {
      language: spec.framework,
      maxTokens: 2000,
      temperature: 0.3
    });

    return this.postProcessComponentCode(response, spec, options);
  }

  private buildComponentPrompt(spec: ComponentSpec, context: any, options: GenerationOptions): string {
    let prompt = `Generate a modern ${spec.framework} component named "${spec.name}" with the following specifications:

Component Type: ${spec.type}
Framework: ${spec.framework}
Styling: ${spec.styling}

Requirements:
- Follow ${spec.framework} best practices and modern patterns
- Use TypeScript for type safety
- Implement proper error boundaries and loading states
- Follow semantic HTML structure
- Ensure component is reusable and composable`;

    if (spec.props && spec.props.length > 0) {
      prompt += `\n\nProps Interface:
${spec.props.map(prop => `- ${prop.name}: ${prop.type}${prop.required ? ' (required)' : ' (optional)'} - ${prop.description}`).join('\n')}`;
    }

    if (options.ensureAccessibility) {
      prompt += `\n\nAccessibility Requirements:
- WCAG 2.1 AA compliance
- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management`;
    }

    if (options.makeResponsive) {
      prompt += `\n\nResponsive Design:
- Mobile-first approach
- Flexible layouts using CSS Grid/Flexbox
- Responsive typography and spacing
- Touch-friendly interactions`;
    }

    if (options.optimizePerformance) {
      prompt += `\n\nPerformance Optimization:
- Lazy loading where appropriate
- Memoization for expensive calculations
- Efficient re-rendering strategies
- Bundle size optimization`;
    }

    if (context.designTokens) {
      prompt += `\n\nDesign System:
Use the following design tokens:
${JSON.stringify(context.designTokens, null, 2)}`;
    }

    if (context.modernPatterns && context.modernPatterns.patterns.length > 0) {
      prompt += `\n\nModern Patterns to Consider:
${context.modernPatterns.patterns.map((p: any) => `- ${p.name}: ${p.description}`).join('\n')}`;
    }

    prompt += `\n\nGenerate clean, production-ready code with proper comments and documentation.`;

    return prompt;
  }

  private async generateStyles(spec: ComponentSpec, context: any, options: GenerationOptions): Promise<string> {
    const prompt = `Generate ${spec.styling} styles for the ${spec.name} component.

Requirements:
- Modern CSS features (Grid, Flexbox, Custom Properties)
- Responsive design with mobile-first approach
- Smooth transitions and micro-interactions
- Consistent with design system tokens
- Performance optimized (minimal reflows/repaints)
- Dark mode support where applicable

${context.designTokens ? `Design Tokens:\n${JSON.stringify(context.designTokens, null, 2)}` : ''}

Generate clean, maintainable styles with proper organization and comments.`;

    const response = await this.aiService.generateCode(prompt, {
      language: 'css',
      maxTokens: 1500,
      temperature: 0.2
    });

    return response;
  }

  private async parsePromptToSpec(prompt: string): Promise<ComponentSpec> {
    const parsePrompt = `Parse this natural language prompt and extract component specifications:

"${prompt}"

Return a JSON object with the following structure:
{
  "name": "ComponentName",
  "type": "functional|class|hook|utility",
  "framework": "react|vue|angular|svelte|vanilla",
  "styling": "css|scss|tailwind|styled-components|emotion|css-modules",
  "props": [{"name": "propName", "type": "string", "required": true, "description": "prop description"}],
  "features": [{"name": "featureName", "description": "feature description", "implementation": "implementation details"}],
  "accessibility": true,
  "responsive": true,
  "animations": false,
  "testing": true,
  "storybook": true,
  "documentation": true
}

Infer reasonable defaults based on the prompt context.`;

    const response = await this.aiService.generateCode(parsePrompt, {
      language: 'json',
      maxTokens: 1000,
      temperature: 0.1
    });

    try {
      return JSON.parse(response);
    } catch (error) {
      throw new Error('Failed to parse component specification from prompt');
    }
  }

  private postProcessComponentCode(code: string, spec: ComponentSpec, options: GenerationOptions): string {
    // Remove any markdown formatting
    code = code.replace(/```[\w]*\n?/g, '').trim();

    // Add imports if missing
    if (spec.framework === 'react' && !code.includes('import React')) {
      code = `import React from 'react';\n${code}`;
    }

    // Add TypeScript types if needed
    if (options.includeTypes && !code.includes('interface') && !code.includes('type')) {
      const interfaceName = `${spec.name}Props`;
      const propsInterface = this.generatePropsInterface(spec.props || [], interfaceName);
      code = `${propsInterface}\n\n${code}`;
    }

    return code;
  }

  private generatePropsInterface(props: PropDefinition[], interfaceName: string): string {
    if (props.length === 0) {
      return `interface ${interfaceName} {}`;
    }

    const propsString = props.map(prop => {
      const optional = prop.required ? '' : '?';
      return `  ${prop.name}${optional}: ${prop.type};`;
    }).join('\n');

    return `interface ${interfaceName} {\n${propsString}\n}`;
  }

  private getComponentFilename(spec: ComponentSpec): string {
    const extensions = {
      react: 'tsx',
      vue: 'vue',
      angular: 'component.ts',
      svelte: 'svelte',
      vanilla: 'js'
    };

    return `${spec.name}.${extensions[spec.framework]}`;
  }

  private getStylesFilename(spec: ComponentSpec): string {
    const extensions = {
      css: 'css',
      scss: 'scss',
      tailwind: 'css',
      'styled-components': 'styles.ts',
      emotion: 'styles.ts',
      'css-modules': 'module.css'
    };

    return `${spec.name}.${extensions[spec.styling]}`;
  }

  private getLanguageForFramework(framework: string): string {
    const languages = {
      react: 'typescript',
      vue: 'vue',
      angular: 'typescript',
      svelte: 'svelte',
      vanilla: 'javascript'
    };

    return languages[framework] || 'typescript';
  }

  private getStylesLanguage(styling: string): string {
    const languages = {
      css: 'css',
      scss: 'scss',
      tailwind: 'css',
      'styled-components': 'typescript',
      emotion: 'typescript',
      'css-modules': 'css'
    };

    return languages[styling] || 'css';
  }

  private getFileExtension(framework: string): string {
    const extensions = {
      react: 'tsx',
      vue: 'vue',
      angular: 'ts',
      svelte: 'svelte',
      vanilla: 'js'
    };

    return extensions[framework] || 'tsx';
  }

  private getTestFilename(spec: ComponentSpec): string {
    return `${spec.name}.test.${this.getFileExtension(spec.framework)}`;
  }

  private async generateTests(spec: ComponentSpec, context: any): Promise<string> {
    // Implementation for test generation
    return `// Tests for ${spec.name} component\n// Implementation would be generated here`;
  }

  private async generateStories(spec: ComponentSpec, context: any): Promise<string> {
    // Implementation for Storybook stories generation
    return `// Storybook stories for ${spec.name} component\n// Implementation would be generated here`;
  }

  private async generateDocumentation(spec: ComponentSpec, context: any): Promise<string> {
    // Implementation for documentation generation
    return `# ${spec.name} Component\n\nDocumentation would be generated here`;
  }

  private async generateTypes(spec: ComponentSpec, context: any): Promise<string> {
    // Implementation for TypeScript types generation
    return `// TypeScript types for ${spec.name} component\n// Implementation would be generated here`;
  }

  private async analyzeDependencies(spec: ComponentSpec, options: GenerationOptions): Promise<{runtime: string[], dev: string[]}> {
    // Implementation for dependency analysis
    return {
      runtime: [],
      dev: []
    };
  }

  private async generateUsageExamples(spec: ComponentSpec, context: any): Promise<string> {
    // Implementation for usage examples generation
    return `// Usage examples for ${spec.name} component\n// Implementation would be generated here`;
  }

  private async generateMetadata(spec: ComponentSpec, context: any, options: GenerationOptions): Promise<ComponentMetadata> {
    // Implementation for metadata generation
    return {
      category: 'ui',
      complexity: 'medium',
      performance: 'high',
      accessibility: options.ensureAccessibility,
      responsive: options.makeResponsive,
      browserSupport: ['Chrome', 'Firefox', 'Safari', 'Edge'],
      estimatedSize: '5KB',
      renderTime: '<16ms'
    };
  }

  private async createVariantSpec(baseComponent: GeneratedComponent, variantType: string): Promise<ComponentSpec> {
    // Implementation for creating variant specifications
    return {
      name: `${baseComponent.name}${variantType}`,
      type: 'functional',
      framework: baseComponent.framework as any,
      styling: 'css',
      accessibility: true,
      responsive: true
    };
  }
}
