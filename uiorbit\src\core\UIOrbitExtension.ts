import * as vscode from 'vscode';
import { ServiceRegistry } from './ServiceRegistry';
import { ConfigurationService } from '../services/ConfigurationService';
import { ChatWebviewProvider } from '../webview/ChatWebviewProvider';
import { UIOrbitWebviewProvider } from '../webview/UIOrbitWebviewProvider';
import { CommandService } from '../services/CommandService';
import { FileOperationsService } from '../services/FileOperationsService';
import { WorkspaceAnalysisService } from '../services/WorkspaceAnalysisService';
import { ProjectDetectionService } from '../services/ProjectDetectionService';
import { CodeGenerationService } from '../services/CodeGenerationService';
import { TemplateEngineService } from '../services/TemplateEngineService';
import { ComponentLibraryService } from '../services/ComponentLibraryService';
import { AIService } from '../services/AIService';
import { UsageTrackingService } from '../services/UsageTrackingService';
import { FrontendContextEngine } from '../services/FrontendContextEngine';
import { ProjectGenerationService } from '../services/ProjectGenerationService';
import { PreviewService } from '../services/PreviewService';
import { TrendIntelligenceEngine } from '../services/TrendIntelligenceEngine';
import { ASTAnalysisService } from '../services/ASTAnalysisService';
import { VectorDatabaseService } from '../services/VectorDatabaseService';
import { ContextEngineService } from '../services/ContextEngineService';
import { FileWatchingService } from '../services/FileWatchingService';
import { DesignSystemAnalyzer } from '../services/DesignSystemAnalyzer';
import { ModernUIKnowledgeBase } from '../services/ModernUIKnowledgeBase';
import { AdvancedComponentGenerator } from '../services/AdvancedComponentGenerator';
import { WebsiteCloneService } from '../services/WebsiteCloneService';
import { ImageToFrontendService } from '../services/ImageToFrontendService';
import { Logger } from '../utils/Logger';

/**
 * Main extension class that orchestrates all UIOrbit functionality
 * Implements the service registry pattern for clean architecture
 */
export class UIOrbitExtension {
  private serviceRegistry: ServiceRegistry;
  private chatProvider: ChatWebviewProvider | undefined;
  private webviewProvider: UIOrbitWebviewProvider | undefined;
  private disposables: vscode.Disposable[] = [];

  constructor(private context: vscode.ExtensionContext) {
    this.serviceRegistry = new ServiceRegistry();
  }

  /**
   * Activate the extension and initialize all services
   */
  async activate(): Promise<void> {
    try {
      Logger.info('Initializing UIOrbit extension...');

      // Initialize core services
      await this.initializeServices();

      // Setup webview provider
      await this.setupWebviewProvider();

      // Register commands
      await this.registerCommands();

      // Setup event listeners
      this.setupEventListeners();

      Logger.info('UIOrbit extension initialization completed');

    } catch (error) {
      Logger.error('Failed to activate UIOrbit extension:', error);
      throw error;
    }
  }

  /**
   * Deactivate the extension and cleanup resources
   */
  async deactivate(): Promise<void> {
    try {
      Logger.info('Deactivating UIOrbit extension...');

      // Stop file watching
      const fileWatchingService = this.serviceRegistry.get<FileWatchingService>('fileWatching');
      if (fileWatchingService) {
        fileWatchingService.stopWatching();
      }

      // Dispose all disposables
      this.disposables.forEach(disposable => disposable.dispose());
      this.disposables = [];

      // Cleanup services
      await this.serviceRegistry.dispose();

      Logger.info('UIOrbit extension deactivated');

    } catch (error) {
      Logger.error('Error during extension deactivation:', error);
      throw error;
    }
  }

  /**
   * Initialize all core services
   */
  private async initializeServices(): Promise<void> {
    Logger.info('Initializing core services...');

    // Configuration service
    const configService = new ConfigurationService();
    this.serviceRegistry.register('configuration', configService);
    await configService.initialize();

    // File operations service
    const fileOpsService = new FileOperationsService();
    this.serviceRegistry.register('fileOperations', fileOpsService);

    // Workspace analysis service
    const workspaceAnalysisService = new WorkspaceAnalysisService(fileOpsService);
    this.serviceRegistry.register('workspaceAnalysis', workspaceAnalysisService);

    // Project detection service
    const projectDetectionService = new ProjectDetectionService(fileOpsService, workspaceAnalysisService);
    this.serviceRegistry.register('projectDetection', projectDetectionService);

    // Usage tracking service
    const usageTrackingService = new UsageTrackingService(this.context);
    this.serviceRegistry.register('usageTracking', usageTrackingService);
    await usageTrackingService.initialize();

    // Frontend context engine
    const contextEngine = new FrontendContextEngine(fileOpsService);
    this.serviceRegistry.register('contextEngine', contextEngine);
    await contextEngine.initialize();

    // AI service (needed for code generation)
    const aiService = new AIService(configService);
    aiService.setProjectDetectionService(projectDetectionService);
    aiService.setUsageTrackingService(usageTrackingService);
    this.serviceRegistry.register('ai', aiService);

    // Project generation service
    const projectGenerationService = new ProjectGenerationService(
      aiService,
      contextEngine,
      fileOpsService
    );
    this.serviceRegistry.register('projectGeneration', projectGenerationService);

    // Preview service
    const previewService = new PreviewService(
      fileOpsService,
      contextEngine,
      this.context
    );
    this.serviceRegistry.register('preview', previewService);

    // Trend intelligence engine
    const trendEngine = new TrendIntelligenceEngine(
      aiService,
      contextEngine,
      this.context
    );
    this.serviceRegistry.register('trendEngine', trendEngine);
    await trendEngine.initialize();

    // Template engine service
    const templateEngineService = new TemplateEngineService();
    this.serviceRegistry.register('templateEngine', templateEngineService);

    // Code generation service
    const codeGenerationService = new CodeGenerationService(aiService, projectDetectionService, fileOpsService);
    this.serviceRegistry.register('codeGeneration', codeGenerationService);

    // Component library service
    const componentLibraryService = new ComponentLibraryService(fileOpsService, templateEngineService, projectDetectionService);
    this.serviceRegistry.register('componentLibrary', componentLibraryService);

    // Phase 2: Core Intelligence Services
    Logger.info('Initializing Phase 2 Core Intelligence services...');

    // AST Analysis service
    const astAnalysisService = new ASTAnalysisService();
    this.serviceRegistry.register('astAnalysis', astAnalysisService);

    // Vector Database service
    const vectorDatabaseService = new VectorDatabaseService(astAnalysisService);
    this.serviceRegistry.register('vectorDatabase', vectorDatabaseService);

    // Context Engine service
    const contextEngineService = new ContextEngineService(
      astAnalysisService,
      vectorDatabaseService,
      workspaceAnalysisService
    );
    this.serviceRegistry.register('contextEngineService', contextEngineService);

    // File Watching service
    const fileWatchingService = new FileWatchingService(
      astAnalysisService,
      vectorDatabaseService,
      contextEngineService
    );
    this.serviceRegistry.register('fileWatching', fileWatchingService);

    // Start file watching
    await fileWatchingService.startWatching();

    // Start initial workspace indexing
    Logger.info('Starting initial workspace indexing...');
    await vectorDatabaseService.indexWorkspace();

    // Phase 3: UI/UX Intelligence Services
    Logger.info('Initializing Phase 3 UI/UX Intelligence services...');

    // Design System Analyzer
    const designSystemAnalyzer = new DesignSystemAnalyzer(this.serviceRegistry);
    this.serviceRegistry.register('designSystemAnalyzer', designSystemAnalyzer);

    // Modern UI Knowledge Base
    const modernUIKnowledgeBase = new ModernUIKnowledgeBase(this.serviceRegistry);
    this.serviceRegistry.register('modernUIKnowledgeBase', modernUIKnowledgeBase);

    // Advanced Component Generator
    const advancedComponentGenerator = new AdvancedComponentGenerator(this.serviceRegistry);
    this.serviceRegistry.register('advancedComponentGenerator', advancedComponentGenerator);

    // Phase 4: Revolutionary Features Services
    Logger.info('Initializing Phase 4 Revolutionary Features services...');

    // Website Clone Service
    const websiteCloneService = new WebsiteCloneService(this.serviceRegistry);
    this.serviceRegistry.register('websiteCloneService', websiteCloneService);

    // Image to Frontend Service
    const imageToFrontendService = new ImageToFrontendService(this.serviceRegistry);
    this.serviceRegistry.register('imageToFrontendService', imageToFrontendService);

    Logger.info('All services initialized successfully');
  }

  /**
   * Setup webview providers for chat interface and panels
   */
  private async setupWebviewProvider(): Promise<void> {
    Logger.info('Setting up webview providers...');

    // Create chat webview provider
    this.chatProvider = new ChatWebviewProvider(
      this.context.extensionUri,
      this.serviceRegistry
    );

    // Create multi-panel webview provider
    this.webviewProvider = new UIOrbitWebviewProvider(
      this.context.extensionUri,
      this.serviceRegistry
    );

    // Register the chat webview provider
    const chatProvider = vscode.window.registerWebviewViewProvider(
      'uiorbit.chatView',
      this.chatProvider,
      {
        webviewOptions: {
          retainContextWhenHidden: true
        }
      }
    );

    this.disposables.push(chatProvider);
    this.context.subscriptions.push(chatProvider);

    Logger.info('Webview providers setup completed');
  }

  /**
   * Register all extension commands
   */
  private async registerCommands(): Promise<void> {
    Logger.info('Registering extension commands...');

    const commandService = new CommandService(this.serviceRegistry);

    // Register commands
    const commands = [
      vscode.commands.registerCommand('uiorbit.openChat', async () => {
        if (this.webviewProvider) {
          await this.webviewProvider.createChatPanel();
        } else {
          commandService.openChat();
        }
      }),
      vscode.commands.registerCommand('uiorbit.openPreview', async () => {
        if (this.webviewProvider) {
          await this.webviewProvider.createPreviewPanel();
        }
      }),
      vscode.commands.registerCommand('uiorbit.openDesignSystem', async () => {
        if (this.webviewProvider) {
          await this.webviewProvider.createDesignSystemPanel();
        }
      }),
      vscode.commands.registerCommand('uiorbit.generateComponent', (uri: vscode.Uri) => {
        commandService.generateComponent(uri);
      }),
      vscode.commands.registerCommand('uiorbit.analyzeCode', () => {
        commandService.analyzeCode();
      }),
      vscode.commands.registerCommand('uiorbit.createViteProject', () => {
        commandService.createViteProject();
      }),
      vscode.commands.registerCommand('uiorbit.analyzeWorkspace', () => {
        commandService.analyzeWorkspace();
      }),
      vscode.commands.registerCommand('uiorbit.showWelcome', () => {
        commandService.showWelcome();
      }),
      vscode.commands.registerCommand('uiorbit.showUsageStats', async () => {
        const usageService = this.serviceRegistry.get<UsageTrackingService>('usageTracking');
        if (usageService) {
          await usageService.showUsageStats();
        }
      }),
      vscode.commands.registerCommand('uiorbit.startPreview', async () => {
        const previewService = this.serviceRegistry.get<PreviewService>('preview');
        if (previewService) {
          await previewService.startPreview();
        }
      }),
      vscode.commands.registerCommand('uiorbit.stopPreview', async () => {
        const previewService = this.serviceRegistry.get<PreviewService>('preview');
        if (previewService) {
          await previewService.stopPreview();
        }
      }),
      vscode.commands.registerCommand('uiorbit.generateProject', async () => {
        const description = await vscode.window.showInputBox({
          prompt: 'Describe the project you want to generate',
          placeHolder: 'e.g., AI headshot generation project with modern UI'
        });

        if (description) {
          const projectService = this.serviceRegistry.get<ProjectGenerationService>('projectGeneration');
          if (projectService) {
            try {
              vscode.window.showInformationMessage('🚀 Generating project...');
              const result = await projectService.generateCompleteProject(description, {
                framework: 'react',
                styling: 'tailwind',
                typescript: true,
                animations: true,
                installDependencies: false
              });

              if (result.success) {
                vscode.window.showInformationMessage(`✅ Project "${result.projectName}" generated successfully!`);
                // Open the generated project
                const uri = vscode.Uri.file(result.projectPath);
                await vscode.commands.executeCommand('vscode.openFolder', uri, true);
              }
            } catch (error) {
              vscode.window.showErrorMessage(`❌ Failed to generate project: ${error}`);
            }
          }
        }
      }),
      vscode.commands.registerCommand('uiorbit.analyzeTrends', async () => {
        const trendEngine = this.serviceRegistry.get<TrendIntelligenceEngine>('trendEngine');
        if (trendEngine) {
          try {
            vscode.window.showInformationMessage('🔍 Analyzing UI/UX trends...');
            const analysis = await trendEngine.analyzeProjectTrends();

            // Show results in a new document
            const doc = await vscode.workspace.openTextDocument({
              content: this.formatTrendAnalysis(analysis),
              language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
          } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to analyze trends: ${error}`);
          }
        }
      }),
      vscode.commands.registerCommand('uiorbit.modernizeSuggestions', async () => {
        const trendEngine = this.serviceRegistry.get<TrendIntelligenceEngine>('trendEngine');
        if (trendEngine) {
          try {
            vscode.window.showInformationMessage('💡 Generating modernization suggestions...');
            const suggestions = await trendEngine.getModernizationSuggestions();

            // Show suggestions in a new document
            const doc = await vscode.workspace.openTextDocument({
              content: this.formatModernizationSuggestions(suggestions),
              language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
          } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to generate suggestions: ${error}`);
          }
        }
      }),
      vscode.commands.registerCommand('uiorbit.generateTrendComponent', async () => {
        const description = await vscode.window.showInputBox({
          prompt: 'Describe the component you want to generate',
          placeHolder: 'e.g., modern hero section with glassmorphism effect'
        });

        if (description) {
          const trendEngine = this.serviceRegistry.get<TrendIntelligenceEngine>('trendEngine');
          if (trendEngine) {
            try {
              vscode.window.showInformationMessage('✨ Generating trend-aware component...');
              const component = await trendEngine.generateTrendAwareComponent(description);

              // Create new file with the component
              const fileName = `${component.name}.tsx`;
              const doc = await vscode.workspace.openTextDocument({
                content: component.code,
                language: 'typescriptreact'
              });
              await vscode.window.showTextDocument(doc);

              vscode.window.showInformationMessage(
                `✅ Generated ${component.name} with trends: ${component.trendsApplied.join(', ')}`
              );
            } catch (error) {
              vscode.window.showErrorMessage(`❌ Failed to generate component: ${error}`);
            }
          }
        }
      }),
      vscode.commands.registerCommand('uiorbit.generateModernComponent', () => {
        commandService.generateModernComponent();
      }),
      vscode.commands.registerCommand('uiorbit.browseComponentLibrary', () => {
        commandService.browseComponentLibrary();
      }),
      vscode.commands.registerCommand('uiorbit.optimizeCode', () => {
        commandService.optimizeCode();
      })
    ];

    // Add to disposables
    commands.forEach(command => {
      this.disposables.push(command);
      this.context.subscriptions.push(command);
    });

    Logger.info('Extension commands registered');
  }

  /**
   * Setup event listeners for workspace changes
   */
  private setupEventListeners(): void {
    Logger.info('Setting up event listeners...');

    // Listen for configuration changes
    const configWatcher = vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration('uiorbit')) {
        Logger.info('UIOrbit configuration changed, reloading...');
        this.reloadConfiguration();
      }
    });

    this.disposables.push(configWatcher);
    this.context.subscriptions.push(configWatcher);

    Logger.info('Event listeners setup completed');
  }

  /**
   * Reload configuration when settings change
   */
  private async reloadConfiguration(): Promise<void> {
    try {
      const configService = this.serviceRegistry.get<ConfigurationService>('configuration');
      if (configService) {
        await configService.reload();
        Logger.info('Configuration reloaded successfully');
      }
    } catch (error) {
      Logger.error('Failed to reload configuration:', error);
    }
  }

  /**
   * Format trend analysis for display
   */
  private formatTrendAnalysis(analysis: any): string {
    return `# 🔍 UIOrbit Trend Analysis

## 📈 Trending Patterns
${analysis.patterns.map((p: any) => `
### ${p.name}
- **Category**: ${p.category}
- **Popularity**: ${Math.round(p.popularity * 100)}%
- **Status**: ${p.adoption}
- **Description**: ${p.description}
- **Benefits**: ${p.benefits.join(', ')}
`).join('')}

## 🚀 Trending Technologies
${analysis.technologies.map((t: any) => `
### ${t.name}
- **Category**: ${t.category}
- **Popularity**: ${Math.round(t.popularity * 100)}%
- **Growth**: ${Math.round(t.growth * 100)}%
- **Description**: ${t.description}
`).join('')}

## 💡 Recommendations
${analysis.recommendations.map((r: any) => `
### ${r.technology}
- **Action**: ${r.type}
- **Impact**: ${r.impact}
- **Effort**: ${r.effort}
- **Timeline**: ${r.timeline}
- **Reasoning**: ${r.reasoning}
`).join('')}

---
*Generated by UIOrbit Trend Intelligence Engine*
`;
  }

  /**
   * Format modernization suggestions for display
   */
  private formatModernizationSuggestions(suggestions: any[]): string {
    return `# 💡 UIOrbit Modernization Suggestions

${suggestions.map((s: any) => `
## ${s.title}
- **Category**: ${s.category}
- **Impact**: ${s.impact} | **Effort**: ${s.effort} | **Priority**: ${s.priority}
- **Trend Alignment**: ${s.trendAlignment}

### Description
${s.description}

### Implementation
${s.implementation}

${s.codeExample ? `### Code Example
\`\`\`typescript
${s.codeExample}
\`\`\`` : ''}

### Benefits
${s.benefits.map((b: string) => `- ${b}`).join('\n')}

---
`).join('')}

*Generated by UIOrbit Trend Intelligence Engine*
`;
  }
}
