Aah shit here we go again 
PS D:\Project\New folder\uiorbit> npm run compile

> uiorbit@1.0.0 compile
> webpack

    [webpack-cli] Compiler starting... 
    [webpack-cli] Compiler is using config: 'D:\Project\New folder\uiorbit\webpack.config.js'
    [webpack-cli] Compiler starting... 
    [webpack-cli] Compiler is using config: 'D:\Project\New folder\uiorbit\webpack.config.js'
    [webpack-cli] Compiler finished
    [webpack-cli] Compiler finished
asset extension.js 11.5 MiB [compared for emit] (name: main) 1 related asset
runtime modules 793 bytes 4 modules
modules by path ./node_modules/ 11 MiB
  javascript modules 10.8 MiB 332 modules
  json modules 255 KiB
    ./node_modules/@babel/helper-globals/data/builtin-lower.json 172 bytes [built] [code generated]
    ./node_modules/@babel/helper-globals/data/builtin-upper.json 579 bytes [built] [code generated]
    ./node_modules/tr46/lib/mappingTable.json 254 KiB [built] [code generated]
modules by path ./src/ 519 KiB
  modules by path ./src/services/*.ts 407 KiB 24 modules
  modules by path ./src/core/*.ts 27.1 KiB 2 modules
  modules by path ./src/webview/*.ts 76.1 KiB 2 modules
  ./src/extension.ts 3 KiB [built] [code generated]
  ./src/utils/Logger.ts 5.55 KiB [built] [code generated]
+ 24 modules

WARNING in ./node_modules/typescript/lib/typescript.js 8395:27-46
Critical dependency: the request of a dependency is an expression
 @ ./src/services/ASTAnalysisService.ts 41:24-45
 @ ./src/core/UIOrbitExtension.ts 55:29-70
 @ ./src/extension.ts 39:27-61

1 warning has detailed information that is not shown.
Use 'stats.errorDetails: true' resp. '--stats-error-details' to show it.

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 93:4-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(93,5)
      TS2322: Type 'AIService | undefined' is not assignable to type 'AIService'.
  Type 'undefined' is not assignable to type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 94:4-29
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(94,5)
      TS2322: Type 'DesignSystemAnalyzer | undefined' is not assignable to type 'DesignSystemAnalyzer'.
  Type 'undefined' is not assignable to type 'DesignSystemAnalyzer'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 95:4-22
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(95,5)
      TS2322: Type 'ModernUIKnowledgeBase | undefined' is not assignable to type 'ModernUIKnowledgeBase'.
  Type 'undefined' is not assignable to type 'ModernUIKnowledgeBase'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 96:4-22
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(96,5)
      TS2322: Type 'ContextEngineService | undefined' is not assignable to type 'ContextEngineService'.
  Type 'undefined' is not assignable to type 'ContextEngineService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 187:54-59
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(187,55)
      TS18046: 'error' is of type 'unknown'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 203:57-62
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(203,58)
      TS18046: 'error' is of type 'unknown'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 240:47-64
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(240,48)
      TS2339: Property 'getCurrentContext' does not exist on type 'ContextEngineService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 274:42-54
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(274,43)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 358:42-54
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(358,43)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 390:42-54
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(390,43)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 469:11-31
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(469,12)
      TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ react: string; vue: string; angular: string; svelte: string; vanilla: string; }'.
  No index signature with a parameter of type 'string' was found on type '{ react: string; vue: string; angular: string; svelte: string; vanilla: string; }'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 482:11-29
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(482,12)
      TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ css: string; scss: string; tailwind: string; 'styled-components': string; emotion: string; 'css-modules': string; }'.
  No index signature with a parameter of type 'string' was found on type '{ css: string; scss: string; tailwind: string; 'styled-components': string; emotion: string; 'css-modules': string; }'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts
./src/services/AdvancedComponentGenerator.ts 494:11-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\AdvancedComponentGenerator.ts(494,12)
      TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ react: string; vue: string; angular: string; svelte: string; vanilla: string; }'.
  No index signature with a parameter of type 'string' was found on type '{ react: string; vue: string; angular: string; svelte: string; vanilla: string; }'.
 @ ./src/core/UIOrbitExtension.ts 61:37-86
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 167:4-16
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(167,5)
      TS2322: Type 'FileOperationsService | undefined' is not assignable to type 'FileOperationsService'.
  Type 'undefined' is not assignable to type 'FileOperationsService'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 196:58-63
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(196,59)
      TS18046: 'error' is of type 'unknown'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 282:41-48
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(282,42)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 293:38-45
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(293,39)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 299:38-45
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(299,39)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 305:38-45
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(305,39)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 430:45-52
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(430,46)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 443:43-50
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(443,44)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 451:45-52
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(451,46)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 458:45-52
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(458,46)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 534:42-49
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(534,43)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 543:41-48
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(543,42)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 648:45-52
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(648,46)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 703:41-48
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(703,42)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 761:41-48
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(761,42)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 818:43-50
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(818,44)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts
./src/services/DesignSystemAnalyzer.ts 825:41-48
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\DesignSystemAnalyzer.ts(825,42)
      TS2345: Argument of type 'FileOperationResult' is not assignable to parameter of type 'string'.
 @ ./src/core/UIOrbitExtension.ts 59:31-74
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts
./src/services/ImageToFrontendService.ts 296:4-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts(296,5)
      TS2322: Type 'AIService | undefined' is not assignable to type 'AIService'.
  Type 'undefined' is not assignable to type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 63:33-78
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts
./src/services/ImageToFrontendService.ts 339:62-67
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts(339,63)
      TS18046: 'error' is of type 'unknown'.
 @ ./src/core/UIOrbitExtension.ts 63:33-78
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts
./src/services/ImageToFrontendService.ts 410:44-56
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts(410,45)
      TS2339: Property 'analyzeImage' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 63:33-78
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts
./src/services/ImageToFrontendService.ts 542:44-56
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\ImageToFrontendService.ts(542,45)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 63:33-78
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\ModernUIKnowledgeBase.ts
./src/services/ModernUIKnowledgeBase.ts 189:10-23
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\ModernUIKnowledgeBase.ts(189,11)
      TS2564: Property 'trendDatabase' has no initializer and is not definitely assigned in the constructor.
 @ ./src/core/UIOrbitExtension.ts 60:32-76
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 187:4-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(187,5)
      TS2322: Type 'AIService | undefined' is not assignable to type 'AIService'.
  Type 'undefined' is not assignable to type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 188:4-16
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(188,5)
      TS2322: Type 'FileOperationsService | undefined' is not assignable to type 'FileOperationsService'.
  Type 'undefined' is not assignable to type 'FileOperationsService'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 227:49-54
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(227,50)
      TS18046: 'error' is of type 'unknown'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 320:44-56
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(320,45)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 421:51-63
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(421,52)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts
./src/services/WebsiteCloneService.ts 468:51-63
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\WebsiteCloneService.ts(468,52)
      TS2339: Property 'generateCode' does not exist on type 'AIService'.
 @ ./src/core/UIOrbitExtension.ts 62:30-72
 @ ./src/extension.ts 39:27-61

41 errors have detailed information that is not shown.
Use 'stats.errorDetails: true' resp. '--stats-error-details' to show it.

webpack 5.99.9 compiled with 41 errors and 1 warning in 12561 ms

asset webview.js 1.57 MiB [compared for emit] (name: main) 1 related asset
runtime modules 1.07 KiB 6 modules
modules by path ./node_modules/ 1.52 MiB
  modules by path ./node_modules/react/ 73.9 KiB 6 modules
  modules by path ./node_modules/react-dom/ 1.42 MiB 6 modules
  modules by path ./node_modules/style-loader/dist/runtime/*.js 5.84 KiB 6 modules
  modules by path ./node_modules/scheduler/ 22 KiB 3 modules
  modules by path ./node_modules/css-loader/dist/runtime/*.js 2.74 KiB 2 modules
modules by path ./src/webview/react/ 29 KiB
  modules by path ./src/webview/react/components/*.tsx 10.5 KiB 5 modules
  modules by path ./src/webview/react/*.tsx 4.11 KiB 2 modules
  modules by path ./src/webview/react/styles/*.css 14.4 KiB
    ./src/webview/react/styles/App.css 1.17 KiB [built] [code generated]
    ./node_modules/css-loader/dist/cjs.js!./src/webview/react/styles/App.css 13.2 KiB [built] [code generated]
webpack 5.99.9 compiled successfully in 10523 ms
PS D:\Project\New folder\uiorbit> 