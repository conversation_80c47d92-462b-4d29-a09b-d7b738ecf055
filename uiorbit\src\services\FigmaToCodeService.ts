import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { AIService } from './AIService';
import { ConfigurationService } from './ConfigurationService';

export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  visible: boolean;
  locked: boolean;
  children?: FigmaNode[];
  absoluteBoundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  fills?: FigmaFill[];
  strokes?: FigmaStroke[];
  effects?: FigmaEffect[];
  cornerRadius?: number;
  characters?: string;
  style?: FigmaTextStyle;
  layoutMode?: 'NONE' | 'HORIZONTAL' | 'VERTICAL';
  primaryAxisSizingMode?: 'FIXED' | 'AUTO';
  counterAxisSizingMode?: 'FIXED' | 'AUTO';
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  itemSpacing?: number;
  layoutGrow?: number;
  constraints?: FigmaConstraints;
}

export interface FigmaFill {
  type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'IMAGE';
  color?: { r: number; g: number; b: number; a: number };
  gradientStops?: Array<{
    position: number;
    color: { r: number; g: number; b: number; a: number };
  }>;
  imageRef?: string;
}

export interface FigmaStroke {
  type: 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL';
  color: { r: number; g: number; b: number; a: number };
  weight: number;
}

export interface FigmaEffect {
  type: 'DROP_SHADOW' | 'INNER_SHADOW' | 'LAYER_BLUR' | 'BACKGROUND_BLUR';
  visible: boolean;
  radius: number;
  color?: { r: number; g: number; b: number; a: number };
  offset?: { x: number; y: number };
}

export interface FigmaTextStyle {
  fontFamily: string;
  fontWeight: number;
  fontSize: number;
  lineHeightPx: number;
  letterSpacing: number;
  textAlignHorizontal: 'LEFT' | 'CENTER' | 'RIGHT' | 'JUSTIFIED';
  textAlignVertical: 'TOP' | 'CENTER' | 'BOTTOM';
}

export interface FigmaConstraints {
  vertical: 'TOP' | 'BOTTOM' | 'CENTER' | 'TOP_BOTTOM' | 'SCALE';
  horizontal: 'LEFT' | 'RIGHT' | 'CENTER' | 'LEFT_RIGHT' | 'SCALE';
}

export interface FigmaDesignFile {
  document: FigmaNode;
  components: { [key: string]: FigmaComponent };
  styles: { [key: string]: FigmaStyle };
  name: string;
  lastModified: string;
  version: string;
}

export interface FigmaComponent {
  key: string;
  name: string;
  description: string;
  componentSetId?: string;
  documentationLinks: Array<{ uri: string; }>;
}

export interface FigmaStyle {
  key: string;
  name: string;
  description: string;
  styleType: 'FILL' | 'TEXT' | 'EFFECT' | 'GRID';
}

export interface FigmaConversionOptions {
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'vanilla';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion';
  typescript: boolean;
  responsive: boolean;
  accessibility: boolean;
  componentLibrary?: 'shadcn' | 'mui' | 'antd' | 'chakra' | 'mantine';
  customPrompt?: string;
  generateTests: boolean;
  generateStories: boolean;
}

export interface ConvertedFigmaDesign {
  components: GeneratedFigmaComponent[];
  styles: GeneratedStyles;
  assets: GeneratedAsset[];
  project: ProjectStructure;
  documentation: string;
  metadata: ConversionMetadata;
}

export interface GeneratedFigmaComponent {
  name: string;
  type: 'component' | 'page' | 'layout';
  code: string;
  styles: string;
  props: ComponentProp[];
  variants?: ComponentVariant[];
  tests?: string;
  stories?: string;
  figmaNodeId: string;
  dependencies: string[];
}

export interface GeneratedStyles {
  designTokens: DesignTokens;
  globalStyles: string;
  componentStyles: { [componentName: string]: string };
}

export interface GeneratedAsset {
  name: string;
  type: 'image' | 'icon' | 'font';
  url: string;
  localPath: string;
  optimized: boolean;
}

export interface ProjectStructure {
  framework: string;
  styling: string;
  packageJson: string;
  configFiles: { [filename: string]: string };
  folderStructure: { [path: string]: string[] };
}

export interface ConversionMetadata {
  figmaFileId: string;
  figmaFileName: string;
  conversionDate: Date;
  totalComponents: number;
  framework: string;
  styling: string;
  estimatedDevelopmentTime: string;
}

export interface ComponentProp {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description: string;
}

export interface ComponentVariant {
  name: string;
  props: { [key: string]: any };
  description: string;
}

export interface DesignTokens {
  colors: { [name: string]: string };
  typography: { [name: string]: any };
  spacing: { [name: string]: string };
  shadows: { [name: string]: string };
  borderRadius: { [name: string]: string };
}

export class FigmaToCodeService {
  private aiService: AIService;
  private configService: ConfigurationService;
  private figmaApiKey?: string;

  constructor(private serviceRegistry: ServiceRegistry) {
    this.aiService = serviceRegistry.get<AIService>('ai')!;
    this.configService = serviceRegistry.get<ConfigurationService>('configuration')!;
    this.initializeFigmaApi();
  }

  private async initializeFigmaApi(): Promise<void> {
    this.figmaApiKey = await this.configService.getConfiguration('figma.apiKey');
    if (!this.figmaApiKey) {
      Logger.warn('Figma API key not configured. Some features may not work.');
    }
  }

  /**
   * Convert Figma design to code from file ID
   */
  async convertFromFigmaFile(fileId: string, options: FigmaConversionOptions): Promise<ConvertedFigmaDesign> {
    Logger.info(`Converting Figma file: ${fileId}`);

    try {
      // 1. Fetch design from Figma API
      const designFile = await this.fetchFigmaFile(fileId);

      // 2. Parse and analyze the design
      const analysis = await this.analyzeFigmaDesign(designFile);

      // 3. Convert to code
      const converted = await this.generateCodeFromAnalysis(analysis, options);

      Logger.info(`Figma conversion completed for file: ${fileId}`);
      return converted;

    } catch (error) {
      Logger.error(`Figma conversion failed for ${fileId}:`, error);
      throw new Error(`Figma conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert from uploaded Figma JSON file
   */
  async convertFromJson(jsonContent: string, options: FigmaConversionOptions): Promise<ConvertedFigmaDesign> {
    Logger.info('Converting from Figma JSON content');

    try {
      // 1. Parse JSON content
      const designFile: FigmaDesignFile = JSON.parse(jsonContent);

      // 2. Analyze the design
      const analysis = await this.analyzeFigmaDesign(designFile);

      // 3. Convert to code
      const converted = await this.generateCodeFromAnalysis(analysis, options);

      Logger.info('Figma JSON conversion completed');
      return converted;

    } catch (error) {
      Logger.error('Figma JSON conversion failed:', error);
      throw new Error(`JSON conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert specific Figma component with custom prompt
   */
  async convertWithPrompt(
    figmaData: string | FigmaDesignFile,
    prompt: string,
    options: FigmaConversionOptions
  ): Promise<ConvertedFigmaDesign> {
    Logger.info('Converting Figma design with custom prompt');

    try {
      // Parse data if it's a string
      const designFile: FigmaDesignFile = typeof figmaData === 'string'
        ? JSON.parse(figmaData) as FigmaDesignFile
        : figmaData as FigmaDesignFile;

      // Add custom prompt to options
      const enhancedOptions = { ...options, customPrompt: prompt };

      // Analyze and convert
      const analysis = await this.analyzeFigmaDesign(designFile);
      const converted = await this.generateCodeFromAnalysis(analysis, enhancedOptions);

      Logger.info('Prompt-based Figma conversion completed');
      return converted;

    } catch (error) {
      Logger.error('Prompt-based conversion failed:', error);
      throw new Error(`Prompt conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async fetchFigmaFile(fileId: string): Promise<FigmaDesignFile> {
    if (!this.figmaApiKey) {
      throw new Error('Figma API key not configured');
    }

    try {
      const response = await fetch(`https://api.figma.com/v1/files/${fileId}`, {
        headers: {
          'X-Figma-Token': this.figmaApiKey
        }
      });

      if (!response.ok) {
        throw new Error(`Figma API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      Logger.error('Failed to fetch Figma file:', error);
      throw new Error(`Failed to fetch Figma file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async analyzeFigmaDesign(designFile: FigmaDesignFile): Promise<any> {
    Logger.info('Analyzing Figma design structure...');

    const analysis = {
      components: this.extractComponents(designFile.document),
      designTokens: this.extractDesignTokens(designFile),
      layout: this.analyzeLayout(designFile.document),
      typography: this.extractTypography(designFile),
      colors: this.extractColors(designFile),
      spacing: this.extractSpacing(designFile.document),
      assets: this.extractAssets(designFile.document)
    };

    return analysis;
  }

  private extractComponents(node: FigmaNode): any[] {
    const components: any[] = [];

    const traverse = (currentNode: FigmaNode, depth: number = 0) => {
      // Identify component-like structures
      if (this.isComponent(currentNode)) {
        components.push({
          id: currentNode.id,
          name: currentNode.name,
          type: currentNode.type,
          bounds: currentNode.absoluteBoundingBox,
          children: currentNode.children?.length || 0,
          depth
        });
      }

      // Recursively traverse children
      if (currentNode.children) {
        currentNode.children.forEach(child => traverse(child, depth + 1));
      }
    };

    traverse(node);
    return components;
  }

  private isComponent(node: FigmaNode): boolean {
    // Heuristics to identify components
    return (
      node.type === 'COMPONENT' ||
      node.type === 'COMPONENT_SET' ||
      node.type === 'INSTANCE' ||
      (node.type === 'FRAME' && (node.children?.length || 0) > 0) ||
      (node.type === 'GROUP' && (node.children?.length || 0) > 1)
    );
  }

  private extractDesignTokens(designFile: FigmaDesignFile): DesignTokens {
    return {
      colors: this.extractColorTokens(designFile),
      typography: this.extractTypographyTokens(designFile),
      spacing: this.extractSpacingTokens(designFile),
      shadows: this.extractShadowTokens(designFile),
      borderRadius: this.extractBorderRadiusTokens(designFile)
    };
  }

  private extractColorTokens(designFile: FigmaDesignFile): { [name: string]: string } {
    const colors: { [name: string]: string } = {};

    // Extract from styles
    Object.entries(designFile.styles || {}).forEach(([key, style]) => {
      if (style.styleType === 'FILL') {
        colors[style.name] = this.convertFigmaColorToHex({ r: 0, g: 0, b: 0, a: 1 }); // Placeholder
      }
    });

    return colors;
  }

  private convertFigmaColorToHex(color: { r: number; g: number; b: number; a: number }): string {
    const r = Math.round(color.r * 255);
    const g = Math.round(color.g * 255);
    const b = Math.round(color.b * 255);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  private extractTypographyTokens(designFile: FigmaDesignFile): { [name: string]: any } {
    const typography: { [name: string]: any } = {};

    Object.entries(designFile.styles || {}).forEach(([key, style]) => {
      if (style.styleType === 'TEXT') {
        typography[style.name] = {
          fontFamily: 'Inter', // Placeholder
          fontSize: '16px',
          fontWeight: 400,
          lineHeight: '1.5'
        };
      }
    });

    return typography;
  }

  private extractSpacingTokens(designFile: FigmaDesignFile): { [name: string]: string } {
    // Extract spacing patterns from layout
    return {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px'
    };
  }

  private extractShadowTokens(designFile: FigmaDesignFile): { [name: string]: string } {
    return {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)'
    };
  }

  private extractBorderRadiusTokens(designFile: FigmaDesignFile): { [name: string]: string } {
    return {
      sm: '0.125rem',
      md: '0.375rem',
      lg: '0.5rem',
      full: '9999px'
    };
  }

  private analyzeLayout(node: FigmaNode): any {
    return {
      type: node.layoutMode || 'absolute',
      direction: node.layoutMode === 'HORIZONTAL' ? 'row' : 'column',
      spacing: node.itemSpacing || 0,
      padding: {
        top: node.paddingTop || 0,
        right: node.paddingRight || 0,
        bottom: node.paddingBottom || 0,
        left: node.paddingLeft || 0
      }
    };
  }

  private extractTypography(designFile: FigmaDesignFile): any[] {
    // Extract typography from text nodes
    return [];
  }

  private extractColors(designFile: FigmaDesignFile): string[] {
    // Extract unique colors from fills
    return [];
  }

  private extractSpacing(node: FigmaNode): number[] {
    // Extract spacing values from layout
    return [];
  }

  private extractAssets(node: FigmaNode): any[] {
    // Extract images and other assets
    return [];
  }

  private async generateCodeFromAnalysis(analysis: any, options: FigmaConversionOptions): Promise<ConvertedFigmaDesign> {
    Logger.info('Generating code from Figma analysis...');

    // This will be implemented in the next part
    return {
      components: [],
      styles: {
        designTokens: analysis.designTokens,
        globalStyles: '',
        componentStyles: {}
      },
      assets: [],
      project: {
        framework: options.framework,
        styling: options.styling,
        packageJson: '{}',
        configFiles: {},
        folderStructure: {}
      },
      documentation: '',
      metadata: {
        figmaFileId: '',
        figmaFileName: '',
        conversionDate: new Date(),
        totalComponents: 0,
        framework: options.framework,
        styling: options.styling,
        estimatedDevelopmentTime: '2-4 hours'
      }
    };
  }
}
